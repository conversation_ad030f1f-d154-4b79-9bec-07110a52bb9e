<!--
  占位符模块组件 - Component

  主要功能：
  - 为未开发的功能提供占位符显示
  - 统一的占位符样式和交互
  - 支持自定义图标、标题和描述
-->

<template>
  <div class="placeholder-module">
    <div class="placeholder-content">
      <div class="placeholder-icon">
        <i :class="icon"></i>
      </div>
      
      <div class="placeholder-info">
        <h3 class="placeholder-title">{{ title }}</h3>
        <p class="placeholder-description">{{ description }}</p>
      </div>
      
      <div class="placeholder-actions" v-if="showActions">
        <el-button 
          type="primary" 
          size="small" 
          @click="handleDevelopmentRequest"
          :loading="requesting"
        >
          <i class="el-icon-plus"></i>
          申请开发
        </el-button>
        
        <el-button 
          size="small" 
          @click="handleFeedback"
        >
          <i class="el-icon-chat-dot-round"></i>
          反馈建议
        </el-button>
      </div>
      
      <!-- 开发进度指示器 -->
      <div class="development-progress" v-if="developmentStatus">
        <div class="progress-info">
          <span class="progress-label">开发进度</span>
          <span class="progress-percentage">{{ developmentStatus.progress }}%</span>
        </div>
        <div class="progress-bar">
          <div 
            class="progress-fill" 
            :style="{ width: developmentStatus.progress + '%' }"
          ></div>
        </div>
        <div class="progress-status">
          <i :class="getStatusIcon(developmentStatus.status)"></i>
          <span>{{ getStatusText(developmentStatus.status) }}</span>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: "PlaceholderModule",
  props: {
    // 占位符图标
    icon: {
      type: String,
      default: "el-icon-setting",
    },
    // 占位符标题
    title: {
      type: String,
      default: "功能开发中",
    },
    // 占位符描述
    description: {
      type: String,
      default: "该功能正在开发中，敬请期待",
    },
    // 是否显示操作按钮
    showActions: {
      type: Boolean,
      default: false,
    },
    // 开发状态信息
    developmentStatus: {
      type: Object,
      default: null,
      // 格式: { progress: 30, status: 'planning' | 'developing' | 'testing' | 'completed' }
    },
  },
  data() {
    return {
      requesting: false,
    };
  },
  methods: {
    // 处理开发申请
    async handleDevelopmentRequest() {
      this.requesting = true;
      
      try {
        // 这里可以调用API提交开发申请
        await new Promise(resolve => setTimeout(resolve, 1000)); // 模拟API调用
        
        this.$message.success("开发申请已提交，我们会尽快安排开发");
        this.$emit('development-request', {
          module: this.title,
          description: this.description,
          timestamp: new Date(),
        });
      } catch (error) {
        this.$message.error("提交申请失败，请稍后重试");
      } finally {
        this.requesting = false;
      }
    },

    // 处理反馈建议
    handleFeedback() {
      this.$emit('feedback', {
        module: this.title,
        type: 'suggestion',
        timestamp: new Date(),
      });
      
      // 可以打开反馈弹框或跳转到反馈页面
      this.$message.info("反馈功能即将开放");
    },

    // 获取状态图标
    getStatusIcon(status) {
      const iconMap = {
        planning: 'el-icon-edit-outline',
        developing: 'el-icon-loading',
        testing: 'el-icon-view',
        completed: 'el-icon-check',
      };
      return iconMap[status] || 'el-icon-info';
    },

    // 获取状态文本
    getStatusText(status) {
      const textMap = {
        planning: '需求规划中',
        developing: '开发进行中',
        testing: '测试验证中',
        completed: '开发完成',
      };
      return textMap[status] || '未知状态';
    },
  },
};
</script>

<style lang="scss" scoped>
.placeholder-module {
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 40px 20px;
  background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);

  .placeholder-content {
    text-align: center;
    max-width: 400px;
    width: 100%;

    .placeholder-icon {
      margin-bottom: 24px;

      i {
        font-size: 64px;
        color: #cbd5e0;
        transition: all 0.3s ease;
        
        // 响应式调整
        @media (max-width: 1200px) {
          font-size: 48px;
        }
      }

      &:hover i {
        color: #4299e1;
        transform: scale(1.1);
      }
    }

    .placeholder-info {
      margin-bottom: 32px;

      .placeholder-title {
        font-size: 20px;
        font-weight: 600;
        color: #2d3748;
        margin: 0 0 12px 0;
        
        // 响应式调整
        @media (max-width: 1200px) {
          font-size: 18px;
        }
      }

      .placeholder-description {
        font-size: 14px;
        color: #718096;
        line-height: 1.6;
        margin: 0;
        
        // 响应式调整
        @media (max-width: 1200px) {
          font-size: 13px;
        }
      }
    }

    .placeholder-actions {
      display: flex;
      gap: 12px;
      justify-content: center;
      margin-bottom: 24px;
      
      // 响应式调整
      @media (max-width: 1200px) {
        flex-direction: column;
        align-items: center;
        gap: 8px;
      }

      .el-button {
        border-radius: 6px;
        font-weight: 500;
        
        // 响应式调整
        @media (max-width: 1200px) {
          width: 120px;
        }
      }
    }

    .development-progress {
      background: #ffffff;
      border: 1px solid #e2e8f0;
      border-radius: 8px;
      padding: 16px;
      text-align: left;

      .progress-info {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 8px;

        .progress-label {
          font-size: 13px;
          color: #4a5568;
          font-weight: 500;
        }

        .progress-percentage {
          font-size: 14px;
          color: #3182ce;
          font-weight: 600;
        }
      }

      .progress-bar {
        height: 6px;
        background: #e2e8f0;
        border-radius: 3px;
        overflow: hidden;
        margin-bottom: 12px;

        .progress-fill {
          height: 100%;
          background: linear-gradient(90deg, #4299e1, #3182ce);
          border-radius: 3px;
          transition: width 0.3s ease;
        }
      }

      .progress-status {
        display: flex;
        align-items: center;
        gap: 6px;
        font-size: 12px;
        color: #718096;

        i {
          font-size: 14px;
          color: #4299e1;
        }
      }
    }
  }
}
</style>
