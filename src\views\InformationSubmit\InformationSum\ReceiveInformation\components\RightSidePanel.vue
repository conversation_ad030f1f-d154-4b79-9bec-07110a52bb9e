<!--
  右侧功能面板组件 - Component

  主要功能：
  - 为未来的功能开发提供可扩展的组件结构
  - 支持多个功能模块的集成
  - 提供统一的布局和样式规范
-->

<template>
  <div class="right-side-panel">
    <!-- 功能选项卡 -->
    <div class="panel-tabs" v-if="availableTabs.length > 1">
      <div
        v-for="tab in availableTabs"
        :key="tab.key"
        class="tab-item"
        :class="{ active: activeTab === tab.key }"
        @click="switchTab(tab.key)"
      >
        <i :class="tab.icon"></i>
        <span>{{ tab.label }}</span>
      </div>
    </div>

    <!-- 功能内容区域 -->
    <div class="panel-content">
      <!-- 地图功能模块 -->
      <div v-if="activeTab === 'map'" class="content-module">
        <MapModule
          v-if="enabledModules.includes('map')"
          :detail-data="detailData"
          :process-data="processData"
          @location-select="handleLocationSelect"
        />
        <PlaceholderModule
          v-else
          icon="el-icon-location"
          title="地图定位"
          description="事件地点可视化展示"
        />
      </div>

      <!-- 统计分析模块 -->
      <div v-if="activeTab === 'analytics'" class="content-module">
        <AnalyticsModule
          v-if="enabledModules.includes('analytics')"
          :detail-data="detailData"
          :process-data="processData"
          @chart-update="handleChartUpdate"
        />
        <PlaceholderModule
          v-else
          icon="el-icon-data-analysis"
          title="统计分析"
          description="事件数据统计与分析"
        />
      </div>

      <!-- 相关事件模块 -->
      <div v-if="activeTab === 'related'" class="content-module">
        <RelatedEventsModule
          v-if="enabledModules.includes('related')"
          :detail-data="detailData"
          @event-select="handleEventSelect"
        />
        <PlaceholderModule
          v-else
          icon="el-icon-connection"
          title="相关事件"
          description="关联事件信息展示"
        />
      </div>

      <!-- 文档附件模块 -->
      <div v-if="activeTab === 'documents'" class="content-module">
        <DocumentsModule
          v-if="enabledModules.includes('documents')"
          :detail-data="detailData"
          @document-view="handleDocumentView"
        />
        <PlaceholderModule
          v-else
          icon="el-icon-document"
          title="文档附件"
          description="相关文档和附件管理"
        />
      </div>

      <!-- 默认占位模块 -->
      <div v-if="activeTab === 'placeholder'" class="content-module">
        <PlaceholderModule
          icon="el-icon-setting"
          title="功能开发中"
          description="更多功能正在开发中，敬请期待"
        />
      </div>
    </div>
  </div>
</template>

<script>
// 导入功能模块组件（按需加载）
const MapModule = () => import('./modules/MapModule.vue');
const AnalyticsModule = () => import('./modules/AnalyticsModule.vue');
const RelatedEventsModule = () => import('./modules/RelatedEventsModule.vue');
const DocumentsModule = () => import('./modules/DocumentsModule.vue');
const PlaceholderModule = () => import('./modules/PlaceholderModule.vue');

export default {
  name: "RightSidePanel",
  components: {
    MapModule,
    AnalyticsModule,
    RelatedEventsModule,
    DocumentsModule,
    PlaceholderModule,
  },
  props: {
    detailData: {
      type: Object,
      default: () => ({}),
    },
    processData: {
      type: Array,
      default: () => [],
    },
    // 启用的功能模块列表
    enabledModules: {
      type: Array,
      default: () => [], // 默认为空，显示占位符
    },
  },
  data() {
    return {
      activeTab: 'placeholder',
      // 所有可用的功能选项卡
      allTabs: [
        {
          key: 'map',
          label: '地图',
          icon: 'el-icon-location',
        },
        {
          key: 'analytics',
          label: '分析',
          icon: 'el-icon-data-analysis',
        },
        {
          key: 'related',
          label: '关联',
          icon: 'el-icon-connection',
        },
        {
          key: 'documents',
          label: '文档',
          icon: 'el-icon-document',
        },
        {
          key: 'placeholder',
          label: '开发中',
          icon: 'el-icon-setting',
        },
      ],
    };
  },
  computed: {
    // 根据启用的模块过滤可用选项卡
    availableTabs() {
      if (this.enabledModules.length === 0) {
        return this.allTabs.filter(tab => tab.key === 'placeholder');
      }
      
      const enabledTabs = this.allTabs.filter(tab => 
        this.enabledModules.includes(tab.key)
      );
      
      return enabledTabs.length > 0 ? enabledTabs : [
        this.allTabs.find(tab => tab.key === 'placeholder')
      ];
    },
  },
  watch: {
    enabledModules: {
      handler(newModules) {
        // 当启用模块变化时，自动切换到第一个可用选项卡
        if (newModules.length > 0 && !newModules.includes(this.activeTab)) {
          this.activeTab = newModules[0];
        } else if (newModules.length === 0) {
          this.activeTab = 'placeholder';
        }
      },
      immediate: true,
    },
  },
  methods: {
    // 切换选项卡
    switchTab(tabKey) {
      this.activeTab = tabKey;
      this.$emit('tab-change', tabKey);
    },

    // 地图模块事件处理
    handleLocationSelect(location) {
      this.$emit('location-select', location);
    },

    // 统计分析模块事件处理
    handleChartUpdate(chartData) {
      this.$emit('chart-update', chartData);
    },

    // 相关事件模块事件处理
    handleEventSelect(event) {
      this.$emit('event-select', event);
    },

    // 文档模块事件处理
    handleDocumentView(document) {
      this.$emit('document-view', document);
    },
  },
};
</script>

<style lang="scss" scoped>
.right-side-panel {
  height: 100%;
  display: flex;
  flex-direction: column;
  background: #ffffff;
  border: 1px solid #e2e8f0;
  border-radius: 8px;
  overflow: hidden;
  min-width: 600px;
  
  // 响应式调整
  @media (max-width: 1400px) {
    min-width: 500px;
  }
  
  @media (max-width: 1200px) {
    min-width: 450px;
  }

  .panel-tabs {
    display: flex;
    background: #f8fafc;
    border-bottom: 1px solid #e2e8f0;
    padding: 0 4px;

    .tab-item {
      display: flex;
      align-items: center;
      gap: 6px;
      padding: 12px 16px;
      cursor: pointer;
      border-radius: 6px 6px 0 0;
      transition: all 0.3s ease;
      font-size: 14px;
      color: #64748b;
      position: relative;
      
      // 响应式调整
      @media (max-width: 1200px) {
        padding: 10px 12px;
        font-size: 13px;
        gap: 4px;
      }

      i {
        font-size: 16px;
        
        @media (max-width: 1200px) {
          font-size: 14px;
        }
      }

      &:hover {
        background: rgba(66, 153, 225, 0.1);
        color: #4299e1;
      }

      &.active {
        background: #ffffff;
        color: #3182ce;
        font-weight: 600;
        border-bottom: 2px solid #3182ce;

        &::after {
          content: '';
          position: absolute;
          bottom: -1px;
          left: 0;
          right: 0;
          height: 2px;
          background: #3182ce;
        }
      }
    }
  }

  .panel-content {
    flex: 1;
    overflow: hidden;

    .content-module {
      height: 100%;
      overflow: hidden;
    }
  }
}
</style>
