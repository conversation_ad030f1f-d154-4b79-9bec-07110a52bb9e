<!--
  核心信息展示组件 - Component

  主要功能：
  - 展示事件的核心信息（标题、类型、时间、伤亡情况、地点）
  - 提供格式化的信息展示
  - 支持伤亡情况的特殊显示样式
-->

<template>
  <div class="core-info-compact">
    <div class="info-row title-row" v-if="getEventTitle()">
      <span class="info-text title-text">{{ getEventTitle() }}</span>
    </div>

    <div class="info-row main-info">
      <span class="info-item" v-if="detailData.infoType">
        <i class="el-icon-collection-tag"></i>
        {{ detailData.infoType }}
      </span>

      <span class="info-item time-item" v-if="detailData.infoTime">
        <i class="el-icon-time"></i>
        {{ formatDate(detailData.infoTime) }}
      </span>

      <span
        class="info-item casualty-item"
        v-if="getCasualtyInfo() && getCasualtyInfo() !== '0死0伤'"
      >
        <i class="el-icon-warning"></i>
        {{ getCasualtyInfo() }}
      </span>
    </div>

    <div
      class="info-row location-row"
      v-if="detailData.infoLocationDetail"
    >
      <span class="info-item location-item">
        <i class="el-icon-location"></i>
        {{ detailData.infoLocationDetail }}
      </span>
    </div>
  </div>
</template>

<script>
export default {
  name: "CoreInfoSection",
  props: {
    detailData: {
      type: Object,
      default: () => ({}),
    },
  },
  methods: {
    // 获取事件标题
    getEventTitle() {
      return (
        this.detailData.infoTitle ||
        this.detailData.eventTitle ||
        this.detailData.title ||
        ""
      );
    },

    // 获取伤亡情况信息
    getCasualtyInfo() {
      const death = this.detailData.deathNum || 0;
      const missing = this.detailData.missingNum || 0;
      const severe = this.detailData.severeInjuryNum || 0;
      const light = this.detailData.lightInjuryNum || 0;

      const total = death + missing + severe + light;
      if (total === 0) {
        return "0死0伤";
      }

      return `${death}死${missing}失联${severe}重伤${light}轻伤`;
    },

    // 格式化日期
    formatDate(dateStr) {
      if (!dateStr) return "";
      try {
        const date = new Date(dateStr);
        return date.toLocaleString();
      } catch (e) {
        return dateStr;
      }
    },
  },
};
</script>

<style lang="scss" scoped>
// 简化的核心信息区域
.core-info-compact {
  background: #f8fafc;
  border: 1px solid #e2e8f0;
  border-radius: 8px;
  padding: 12px 16px;
  margin-bottom: 16px;

  .info-row {
    display: flex;
    align-items: center;
    margin-bottom: 8px;

    &:last-child {
      margin-bottom: 0;
    }

    &.title-row {
      margin-bottom: 10px;

      .title-text {
        font-size: 16px;
        font-weight: 600;
        color: #1a202c;
        line-height: 1.4;
      }
    }

    &.main-info {
      flex-wrap: wrap;
      gap: 16px;
    }

    .info-item {
      display: inline-flex;
      align-items: center;
      font-size: 13px;
      color: #4a5568;

      i {
        margin-right: 4px;
        font-size: 14px;
        color: #718096;
      }

      &.casualty-item {
        color: #e53e3e;
        font-weight: 600;

        i {
          color: #e53e3e;
        }
      }

      &.location-item {
        color: #2d3748;
        max-width: 100%;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }
    }
  }
}
</style>
