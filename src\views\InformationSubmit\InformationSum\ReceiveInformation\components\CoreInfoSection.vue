<!--
  核心信息展示组件 - Component

  主要功能：
  - 展示事件的核心信息（标题、类型、时间、伤亡情况、地点）
  - 提供格式化的信息展示
  - 支持伤亡情况的特殊显示样式
-->

<template>
  <div class="core-info-compact">
    <!-- 事件标题 -->
    <div class="title-section" v-if="getEventTitle()">
      <h3 class="event-title">{{ getEventTitle() }}</h3>
    </div>

    <!-- 核心信息网格 -->
    <div class="info-grid">
      <!-- 事件类型 -->
      <div class="info-card" v-if="detailData.infoType">
        <div class="info-icon type-icon">
          <i class="el-icon-collection-tag"></i>
        </div>
        <div class="info-content">
          <div class="info-label">事件类型</div>
          <div class="info-value">{{ detailData.infoType }}</div>
        </div>
      </div>

      <!-- 发生时间 -->
      <div class="info-card" v-if="detailData.infoTime">
        <div class="info-icon time-icon">
          <i class="el-icon-time"></i>
        </div>
        <div class="info-content">
          <div class="info-label">发生时间</div>
          <div class="info-value">{{ formatDate(detailData.infoTime) }}</div>
        </div>
      </div>

      <!-- 伤亡情况 -->
      <div
        class="info-card casualty-card"
        v-if="getCasualtyInfo() && getCasualtyInfo() !== '0死0伤'"
      >
        <div class="info-icon casualty-icon">
          <i class="el-icon-warning"></i>
        </div>
        <div class="info-content">
          <div class="info-label">伤亡情况</div>
          <div class="info-value casualty-value">{{ getCasualtyInfo() }}</div>
        </div>
      </div>
    </div>

    <!-- 事发地点 -->
    <div class="location-section" v-if="detailData.infoLocationDetail">
      <div class="location-card">
        <div class="location-icon">
          <i class="el-icon-location"></i>
        </div>
        <div class="location-content">
          <div class="location-label">事发地点</div>
          <div class="location-value">{{ detailData.infoLocationDetail }}</div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: "CoreInfoSection",
  props: {
    detailData: {
      type: Object,
      default: () => ({}),
    },
  },
  methods: {
    // 获取事件标题
    getEventTitle() {
      return (
        this.detailData.infoTitle ||
        this.detailData.eventTitle ||
        this.detailData.title ||
        ""
      );
    },

    // 获取伤亡情况信息
    getCasualtyInfo() {
      const death = this.detailData.deathNum || 0;
      const missing = this.detailData.missingNum || 0;
      const severe = this.detailData.severeInjuryNum || 0;
      const light = this.detailData.lightInjuryNum || 0;

      const total = death + missing + severe + light;
      if (total === 0) {
        return "0死0伤";
      }

      return `${death}死${missing}失联${severe}重伤${light}轻伤`;
    },

    // 格式化日期
    formatDate(dateStr) {
      if (!dateStr) return "";
      try {
        const date = new Date(dateStr);
        return date.toLocaleString();
      } catch (e) {
        return dateStr;
      }
    },
  },
};
</script>

<style lang="scss" scoped>
// 优化后的核心信息区域
.core-info-compact {
  background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
  border: 1px solid #e2e8f0;
  border-radius: 12px;
  padding: 20px;
  margin-bottom: 16px;
  min-width: 500px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);

  // 响应式调整
  @media (max-width: 1400px) {
    min-width: 450px;
    padding: 16px;
  }

  @media (max-width: 1200px) {
    min-width: 400px;
    padding: 14px;
  }

  // 事件标题区域
  .title-section {
    margin-bottom: 16px;
    padding-bottom: 12px;
    border-bottom: 2px solid #e2e8f0;

    .event-title {
      margin: 0;
      font-size: 18px;
      font-weight: 700;
      color: #1a202c;
      line-height: 1.4;

      // 响应式调整
      @media (max-width: 1200px) {
        font-size: 16px;
      }
    }
  }

  // 信息网格布局
  .info-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 12px;
    margin-bottom: 16px;

    // 响应式调整
    @media (max-width: 1200px) {
      grid-template-columns: repeat(auto-fit, minmax(180px, 1fr));
      gap: 10px;
    }

    .info-card {
      display: flex;
      align-items: center;
      gap: 12px;
      background: #ffffff;
      border: 1px solid #e2e8f0;
      border-radius: 8px;
      padding: 12px;
      transition: all 0.3s ease;

      &:hover {
        border-color: #4299e1;
        box-shadow: 0 4px 12px rgba(66, 153, 225, 0.15);
        transform: translateY(-1px);
      }

      &.casualty-card {
        border-color: #fed7d7;
        background: linear-gradient(135deg, #fffaf0 0%, #fef5e7 100%);

        &:hover {
          border-color: #f56565;
          box-shadow: 0 4px 12px rgba(245, 101, 101, 0.15);
        }
      }

      .info-icon {
        width: 36px;
        height: 36px;
        border-radius: 8px;
        display: flex;
        align-items: center;
        justify-content: center;
        flex-shrink: 0;

        // 响应式调整
        @media (max-width: 1200px) {
          width: 32px;
          height: 32px;
        }

        i {
          font-size: 16px;

          // 响应式调整
          @media (max-width: 1200px) {
            font-size: 14px;
          }
        }

        &.type-icon {
          background: linear-gradient(135deg, #e6f3ff 0%, #bee3f8 100%);
          color: #3182ce;
        }

        &.time-icon {
          background: linear-gradient(135deg, #faf5ff 0%, #e9d8fd 100%);
          color: #805ad5;
        }

        &.casualty-icon {
          background: linear-gradient(135deg, #fed7d7 0%, #feb2b2 100%);
          color: #e53e3e;
        }
      }

      .info-content {
        flex: 1;
        min-width: 0;

        .info-label {
          font-size: 11px;
          color: #718096;
          margin-bottom: 2px;
          font-weight: 500;
          text-transform: uppercase;
          letter-spacing: 0.3px;
        }

        .info-value {
          font-size: 13px;
          color: #2d3748;
          font-weight: 600;
          line-height: 1.3;
          word-break: break-word;

          // 响应式调整
          @media (max-width: 1200px) {
            font-size: 12px;
          }

          &.casualty-value {
            color: #e53e3e;
            font-weight: 700;
          }
        }
      }
    }
  }

  // 地点区域
  .location-section {
    .location-card {
      display: flex;
      align-items: flex-start;
      gap: 12px;
      background: linear-gradient(135deg, #f0fff4 0%, #c6f6d5 100%);
      border: 1px solid #9ae6b4;
      border-radius: 8px;
      padding: 12px;
      transition: all 0.3s ease;

      &:hover {
        border-color: #38a169;
        box-shadow: 0 4px 12px rgba(56, 161, 105, 0.15);
        transform: translateY(-1px);
      }

      .location-icon {
        width: 36px;
        height: 36px;
        border-radius: 8px;
        background: linear-gradient(135deg, #c6f6d5 0%, #9ae6b4 100%);
        color: #38a169;
        display: flex;
        align-items: center;
        justify-content: center;
        flex-shrink: 0;

        // 响应式调整
        @media (max-width: 1200px) {
          width: 32px;
          height: 32px;
        }

        i {
          font-size: 16px;

          // 响应式调整
          @media (max-width: 1200px) {
            font-size: 14px;
          }
        }
      }

      .location-content {
        flex: 1;
        min-width: 0;

        .location-label {
          font-size: 11px;
          color: #2f855a;
          margin-bottom: 2px;
          font-weight: 500;
          text-transform: uppercase;
          letter-spacing: 0.3px;
        }

        .location-value {
          font-size: 13px;
          color: #1a365d;
          font-weight: 600;
          line-height: 1.4;
          word-break: break-word;

          // 响应式调整
          @media (max-width: 1200px) {
            font-size: 12px;
          }
        }
      }
    }
  }
}
</style>
