<!--
  过程详情弹框组件 - Component

  主要功能：
  - 显示单个过程项的详细信息
  - 包含责任部门、事件标题、事发时间地点、事件详情等信息
  - 显示伤亡统计信息
-->

<template>
  <el-dialog
    :visible.sync="dialogVisible"
    title="过程详情"
    width="70%"
    :before-close="handleClose"
    :append-to-body="true"
    :modal-append-to-body="false"
    class="process-detail-dialog"
  >
    <div v-loading="loading" class="process-detail-content">
      <div
        v-if="currentProcessItem && !loading"
        class="detail-content-area"
      >
        <div class="detail-item">
          <div class="detail-label">责任部门：</div>
          <div class="detail-value">
            {{
              currentProcessItem.responsibleDepartment ||
              currentProcessItem.eventReportingUnit ||
              currentProcessItem.originalData?.courseName ||
              "-"
            }}
          </div>
        </div>

        <div class="detail-item">
          <div class="detail-label">事件标题：</div>
          <div class="detail-value">
            {{
              currentProcessItem.eventTitle ||
              currentProcessItem.originalData?.infoTitle ||
              "-"
            }}
          </div>
        </div>

        <div class="detail-item">
          <div class="detail-label">事发时间：</div>
          <div class="detail-value">
            {{ formatDate(currentProcessItem.originalData?.infoTime) || "-" }}
          </div>
        </div>

        <div class="detail-item">
          <div class="detail-label">事发地点：</div>
          <div class="detail-value">
            {{ currentProcessItem.originalData?.infoLocationDetail || "-" }}
          </div>
        </div>

        <div class="detail-item full-content">
          <div class="detail-label">事件详情：</div>
          <div class="detail-value detail-textarea">
            {{
              currentProcessItem.eventInfo ||
              currentProcessItem.originalData?.courseInfo ||
              "暂无详细信息"
            }}
          </div>
        </div>

        <div class="detail-item">
          <div class="detail-label">上报时间：</div>
          <div class="detail-value">
            {{ formatDate(currentProcessItem.createTime) || "-" }}
          </div>
        </div>

        <div class="detail-item">
          <div class="detail-label">上报单位：</div>
          <div class="detail-value">
            {{
              currentProcessItem.eventReportingUnit ||
              currentProcessItem.originalData?.courseName ||
              "-"
            }}
          </div>
        </div>

        <div class="detail-item">
          <div class="detail-label">上报人：</div>
          <div class="detail-value">
            {{ currentProcessItem.eventReportingUser || "-" }}
          </div>
        </div>

        <div class="casualty-summary">
          <div class="casualty-item">
            <span class="casualty-label">死亡：</span>
            <span class="casualty-number death">{{
              currentProcessItem.deathNum || 0
            }}</span>
          </div>
          <div class="casualty-item">
            <span class="casualty-label">失联：</span>
            <span class="casualty-number missing">{{
              currentProcessItem.missingNum || 0
            }}</span>
          </div>
          <div class="casualty-item">
            <span class="casualty-label">重伤：</span>
            <span class="casualty-number severe">{{
              currentProcessItem.severeInjuryNum || 0
            }}</span>
          </div>
          <div class="casualty-item">
            <span class="casualty-label">轻伤：</span>
            <span class="casualty-number light">{{
              currentProcessItem.lightInjuryNum || 0
            }}</span>
          </div>
        </div>
      </div>

      <div v-else-if="!loading" class="no-selection">
        <i class="el-icon-info"></i>
        <p>暂无详情信息</p>
      </div>
    </div>
  </el-dialog>
</template>

<script>
export default {
  name: "ProcessDetailDialog",
  props: {
    visible: {
      type: Boolean,
      default: false,
    },
    currentProcessItem: {
      type: Object,
      default: null,
    },
    loading: {
      type: Boolean,
      default: false,
    },
  },
  computed: {
    dialogVisible: {
      get() {
        return this.visible;
      },
      set(value) {
        this.$emit("update:visible", value);
      },
    },
  },
  methods: {
    handleClose() {
      this.$emit("close");
    },

    // 格式化日期
    formatDate(dateStr) {
      if (!dateStr) return "";
      try {
        const date = new Date(dateStr);
        return date.toLocaleString();
      } catch (e) {
        return dateStr;
      }
    },
  },
};
</script>

<style lang="scss" scoped>
.process-detail-dialog {
  .process-detail-content {
    min-height: 200px;

    .detail-content-area {
      .detail-item {
        display: flex;
        margin-bottom: 16px;
        align-items: flex-start;

        &.full-content {
          flex-direction: column;

          .detail-label {
            margin-bottom: 8px;
          }
        }

        .detail-label {
          font-weight: 600;
          color: #333;
          min-width: 100px;
          flex-shrink: 0;
        }

        .detail-value {
          flex: 1;
          color: #666;
          word-break: break-word;

          &.detail-textarea {
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 4px;
            padding: 12px;
            min-height: 80px;
            line-height: 1.6;
          }
        }
      }

      .casualty-summary {
        display: flex;
        gap: 20px;
        margin-top: 20px;
        padding: 16px;
        background: #f8f9fa;
        border-radius: 8px;
        border: 1px solid #e9ecef;

        .casualty-item {
          display: flex;
          flex-direction: column;
          align-items: center;
          flex: 1;

          .casualty-label {
            font-size: 12px;
            color: #666;
            margin-bottom: 4px;
          }

          .casualty-number {
            font-size: 18px;
            font-weight: 700;

            &.death {
              color: #f56c6c;
            }

            &.missing {
              color: #e6a23c;
            }

            &.severe {
              color: #f56c6c;
            }

            &.light {
              color: #409eff;
            }
          }
        }
      }
    }

    .no-selection {
      text-align: center;
      padding: 40px;
      color: #999;

      i {
        font-size: 48px;
        margin-bottom: 16px;
        display: block;
      }

      p {
        margin: 0;
        font-size: 14px;
      }
    }
  }
}
</style>
