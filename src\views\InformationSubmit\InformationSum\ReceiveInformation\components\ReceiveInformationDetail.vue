<!--
  接报信息详情主组件 - Component

  主要功能：
  - 全屏显示接报信息详情
  - 集成核心信息展示、过程时间轴、过程详情弹框等子组件
  - 处理组件间的数据传递和事件通信
-->

<template>
  <div>
    <el-dialog
      :visible.sync="dialogVisible"
      :title="detailTitle"
      :before-close="handleClose"
      fullscreen
      custom-class="receive-information-detail-dialog"
    >
      <div v-loading="loading" class="detail-content">
        <el-row :gutter="20" class="main-layout">
          <el-col :span="12" class="left-content">
            <!-- 核心信息展示组件 -->
            <CoreInfoSection :detail-data="detailData" />

            <!-- 过程信息时间轴组件 -->
            <ProcessTimeline
              :process-data="processData"
              :process-type-options="processTypeOptions"
              :loading="loading"
              @show-detail="handleShowProcessDetail"
              @select-all="handleSelectAll"
              @item-select="handleItemSelect"
              @type-change="handleProcessTypeChange"
            />
          </el-col>

          <el-col :span="12" class="right-content">
            <!-- 右侧功能面板组件 -->
            <RightSidePanel
              :detail-data="detailData"
              :process-data="processData"
              :enabled-modules="enabledRightModules"
              @tab-change="handleRightTabChange"
              @location-select="handleLocationSelect"
              @chart-update="handleChartUpdate"
              @event-select="handleEventSelect"
              @document-view="handleDocumentView"
            />
          </el-col>
        </el-row>
      </div>
    </el-dialog>

    <!-- 过程详情弹框组件 -->
    <ProcessDetailDialog
      :visible.sync="processDetailDialogVisible"
      :current-process-item="currentProcessItem"
      :loading="detailLoading"
      @close="closeProcessDetailDialog"
    />
  </div>
</template>

<script>
import CoreInfoSection from "./CoreInfoSection.vue";
import ProcessTimeline from "./ProcessTimeline.vue";
import ProcessDetailDialog from "./ProcessDetailDialog.vue";
import RightSidePanel from "./RightSidePanel.vue";
import detailDataMixin from "./mixins/detailDataMixin.js";
import { mapState } from "vuex";

export default {
  name: "ReceiveInformationDetail",
  components: {
    CoreInfoSection,
    ProcessTimeline,
    ProcessDetailDialog,
    RightSidePanel,
  },
  mixins: [detailDataMixin],
  props: {
    dialogVisible: {
      type: Boolean,
      default: false,
    },
    detailId: {
      type: [String, Number],
      default: "",
    },
    detailTitle: {
      type: String,
      default: "接报信息详情",
    },
  },
  data() {
    return {
      processDetailDialogVisible: false,
      currentProcessItem: null,
      // 右侧面板启用的功能模块
      enabledRightModules: [], // 默认为空，显示占位符
      // 可以根据需要启用特定模块，例如: ['map', 'analytics']
    };
  },
  watch: {
    dialogVisible: {
      handler(val) {
        if (val && this.detailId && !this.dataLoaded && !this.loading) {
          this.loadDetailData(this.detailId);
        } else if (!val) {
          this.resetDataState();
        }
      },
      immediate: false,
    },
    detailId: {
      handler(val, oldVal) {
        if (this.dialogVisible && val && val !== oldVal && !this.loading) {
          this.resetDataState();
          this.loadDetailData(val);
        }
      },
      immediate: false,
    },
  },
  computed: {
    ...mapState("user", ["userInfo"]),

    currentUserName() {
      return this.userInfo?.name || "";
    },
  },
  methods: {
    handleClose() {
      this.$emit("close");
    },

    // 处理显示过程详情
    async handleShowProcessDetail(item) {
      const processItem = await this.showProcessDetail(item, this.detailId);
      if (processItem) {
        this.currentProcessItem = processItem;
        this.processDetailDialogVisible = true;
      }
    },

    // 关闭过程详情弹框
    closeProcessDetailDialog() {
      this.processDetailDialogVisible = false;
      this.currentProcessItem = null;
    },

    // 处理全选操作
    handleSelectAll(value) {
      console.log("全选状态:", value);
      // 这里可以添加全选的业务逻辑
    },

    // 处理单项选择
    handleItemSelect({ item, value }) {
      console.log("单项选择:", item, value);
      // 这里可以添加单项选择的业务逻辑
    },

    // 处理过程类型变化
    async handleProcessTypeChange(selectedType) {
      try {
        this.loading = true;
        await this.fetchProcessList(this.detailId, selectedType);
      } catch (error) {
        console.error("过程类型过滤失败:", error);
      } finally {
        this.loading = false;
      }
    },

    // 右侧面板事件处理方法
    handleRightTabChange(tabKey) {
      console.log("右侧面板切换到:", tabKey);
      // 可以在这里添加选项卡切换的业务逻辑
    },

    handleLocationSelect(location) {
      console.log("选择地点:", location);
      // 处理地图模块的地点选择事件
    },

    handleChartUpdate(chartData) {
      console.log("图表更新:", chartData);
      // 处理统计分析模块的图表更新事件
    },

    handleEventSelect(event) {
      console.log("选择相关事件:", event);
      // 处理相关事件模块的事件选择
    },

    handleDocumentView(document) {
      console.log("查看文档:", document);
      // 处理文档模块的文档查看事件
    },
  },
};
</script>

<style lang="scss" scoped>
.detail-content {
  min-height: 600px;
  height: calc(100vh - 120px);
  display: flex;
  flex-direction: column;
  overflow: hidden; // 防止内容溢出

  // 确保在小屏幕下有最小宽度
  min-width: 1200px;

  // 当屏幕宽度不足时，允许水平滚动
  @media (max-width: 1400px) {
    min-width: 1000px;
  }

  @media (max-width: 1200px) {
    min-width: 900px;
  }
}

// 主体布局区域
.main-layout {
  flex: 1;
  height: 100%;
  min-height: 500px;
  overflow: hidden;

  .el-col {
    height: 100%;
    min-width: 600px; // 增加最小宽度，确保内容不被挤压
    overflow: hidden;

    // 当屏幕宽度不足时，允许水平滚动
    @media (max-width: 1400px) {
      min-width: 500px;
    }

    @media (max-width: 1200px) {
      min-width: 450px;
    }
  }

  // 确保整个布局在小屏幕下可以水平滚动
  @media (max-width: 1200px) {
    min-width: 900px; // 两列最小宽度之和
    overflow-x: auto;
  }
}

// 左侧内容区域
.left-content {
  height: 100%;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  min-width: 600px; // 确保左侧内容有足够空间
  padding-right: 10px; // 添加右侧间距

  // 响应式调整
  @media (max-width: 1400px) {
    min-width: 500px;
  }

  @media (max-width: 1200px) {
    min-width: 450px;
    padding-right: 5px;
  }
}

// 右侧内容区域
.right-content {
  height: 100%;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  min-width: 600px; // 确保右侧内容有足够空间
  padding-left: 10px; // 添加左侧间距

  // 响应式调整
  @media (max-width: 1400px) {
    min-width: 500px;
  }

  @media (max-width: 1200px) {
    min-width: 450px;
    padding-left: 5px;
  }
}
</style>

<!-- 全局样式，用于弹框最小宽度设置 -->
<style lang="scss">
.receive-information-detail-dialog {
  .el-dialog {
    min-width: 1200px !important;

    // 响应式调整
    @media (max-width: 1400px) {
      min-width: 1000px !important;
    }

    @media (max-width: 1200px) {
      min-width: 900px !important;
    }
  }

  .el-dialog__body {
    padding: 20px !important;
    overflow: hidden !important;
  }
}
</style>
