<!--
  接报信息详情主组件 - Component

  主要功能：
  - 全屏显示接报信息详情
  - 集成核心信息展示、过程时间轴、过程详情弹框等子组件
  - 处理组件间的数据传递和事件通信
-->

<template>
  <div>
    <el-dialog
      :visible.sync="dialogVisible"
      :title="detailTitle"
      :before-close="handleClose"
      fullscreen
    >
      <div v-loading="loading" class="detail-content">
        <el-row :gutter="20" class="main-layout">
          <el-col :span="12" class="left-content">
            <!-- 核心信息展示组件 -->
            <CoreInfoSection :detail-data="detailData" />

            <!-- 过程信息时间轴组件 -->
            <ProcessTimeline
              :process-data="processData"
              :process-type-options="processTypeOptions"
              :loading="loading"
              @show-detail="handleShowProcessDetail"
              @select-all="handleSelectAll"
              @item-select="handleItemSelect"
              @type-change="handleProcessTypeChange"
            />
          </el-col>

          <el-col :span="12" class="right-content">
            <div class="reserved-section">
              <div class="reserved-content">
                <i class="el-icon-setting"></i>
                <h3>功能开发中</h3>
              </div>
            </div>
          </el-col>
        </el-row>
      </div>
    </el-dialog>

    <!-- 过程详情弹框组件 -->
    <ProcessDetailDialog
      :visible.sync="processDetailDialogVisible"
      :current-process-item="currentProcessItem"
      :loading="detailLoading"
      @close="closeProcessDetailDialog"
    />
  </div>
</template>

<script>
import CoreInfoSection from "./CoreInfoSection.vue";
import ProcessTimeline from "./ProcessTimeline.vue";
import ProcessDetailDialog from "./ProcessDetailDialog.vue";
import detailDataMixin from "./mixins/detailDataMixin.js";
import { mapState } from "vuex";

export default {
  name: "ReceiveInformationDetail",
  components: {
    CoreInfoSection,
    ProcessTimeline,
    ProcessDetailDialog,
  },
  mixins: [detailDataMixin],
  props: {
    dialogVisible: {
      type: Boolean,
      default: false,
    },
    detailId: {
      type: [String, Number],
      default: "",
    },
    detailTitle: {
      type: String,
      default: "接报信息详情",
    },
  },
  data() {
    return {
      processDetailDialogVisible: false,
      currentProcessItem: null,
    };
  },
  watch: {
    dialogVisible: {
      handler(val) {
        if (val && this.detailId && !this.dataLoaded && !this.loading) {
          this.loadDetailData(this.detailId);
        } else if (!val) {
          this.resetDataState();
        }
      },
      immediate: false,
    },
    detailId: {
      handler(val, oldVal) {
        if (this.dialogVisible && val && val !== oldVal && !this.loading) {
          this.resetDataState();
          this.loadDetailData(val);
        }
      },
      immediate: false,
    },
  },
  computed: {
    ...mapState("user", ["userInfo"]),

    currentUserName() {
      return this.userInfo?.name || "";
    },
  },
  methods: {
    handleClose() {
      this.$emit("close");
    },

    // 处理显示过程详情
    async handleShowProcessDetail(item) {
      const processItem = await this.showProcessDetail(item, this.detailId);
      if (processItem) {
        this.currentProcessItem = processItem;
        this.processDetailDialogVisible = true;
      }
    },

    // 关闭过程详情弹框
    closeProcessDetailDialog() {
      this.processDetailDialogVisible = false;
      this.currentProcessItem = null;
    },

    // 处理全选操作
    handleSelectAll(value) {
      console.log("全选状态:", value);
      // 这里可以添加全选的业务逻辑
    },

    // 处理单项选择
    handleItemSelect({ item, value }) {
      console.log("单项选择:", item, value);
      // 这里可以添加单项选择的业务逻辑
    },

    // 处理过程类型变化
    async handleProcessTypeChange(selectedType) {
      try {
        this.loading = true;
        await this.fetchProcessList(this.detailId, selectedType);
      } catch (error) {
        console.error("过程类型过滤失败:", error);
      } finally {
        this.loading = false;
      }
    },
  },
};
</script>

<style lang="scss" scoped>
.detail-content {
  min-height: 600px;
  height: calc(100vh - 120px);
  display: flex;
  flex-direction: column;
}

// 主体布局区域
.main-layout {
  flex: 1;
  height: 100%;
  min-height: 500px;

  .el-col {
    height: 100%;
    min-width: 400px;
  }
}

// 左侧内容区域
.left-content {
  height: 100%;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

// 右侧预留区域
.right-content {
  height: 100%;
  display: flex;
  flex-direction: column;
  overflow: hidden;

  .reserved-section {
    flex: 1;
    display: flex;
    align-items: center;
    justify-content: center;
    background: #f8f9fa;
    border: 1px solid #e9ecef;
    border-radius: 8px;

    .reserved-content {
      text-align: center;
      color: #999;

      i {
        font-size: 48px;
        margin-bottom: 16px;
        display: block;
      }

      h3 {
        margin: 0;
        font-size: 16px;
        color: #666;
      }
    }
  }
}

// 主体布局区域
.main-layout {
  flex: 1;
  height: 100%;
  min-height: 500px;

  .el-col {
    height: 100%;
    min-width: 400px; /* 设置最小宽度 */
  }
}

// 左侧内容区域
.left-content {
  height: 100%;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  // 简化的核心信息区域
  .core-info-compact {
    background: #f8fafc;
    border: 1px solid #e2e8f0;
    border-radius: 8px;
    padding: 12px 16px;
    margin-bottom: 16px;

    .info-row {
      display: flex;
      align-items: center;
      margin-bottom: 8px;

      &:last-child {
        margin-bottom: 0;
      }

      &.title-row {
        margin-bottom: 10px;

        .title-text {
          font-size: 16px;
          font-weight: 600;
          color: #1a202c;
          line-height: 1.4;
        }
      }

      &.main-info {
        flex-wrap: wrap;
        gap: 16px;
      }

      .info-item {
        display: inline-flex;
        align-items: center;
        font-size: 13px;
        color: #4a5568;

        i {
          margin-right: 4px;
          font-size: 14px;
          color: #718096;
        }

        &.casualty-item {
          color: #e53e3e;
          font-weight: 600;

          i {
            color: #e53e3e;
          }
        }

        &.location-item {
          color: #2d3748;
          max-width: 100%;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
        }
      }
    }
  }

  .core-info-section {
    background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
    border: 1px solid #e2e8f0;
    border-radius: 12px;
    padding: 20px;
    margin-bottom: 16px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
    transition: all 0.3s ease;

    &:hover {
      box-shadow: 0 8px 25px rgba(0, 0, 0, 0.08);
      transform: translateY(-2px);
    }

    .section-title {
      display: flex;
      align-items: center;
      margin-bottom: 16px;
      font-size: 16px;
      font-weight: 600;
      color: #1a202c;

      i {
        margin-right: 8px;
        color: #4299e1;
        font-size: 18px;
      }

      span {
        background: linear-gradient(135deg, #4299e1 0%, #3182ce 100%);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        background-clip: text;
      }
    }

    .core-info-grid {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
      gap: 12px;

      .core-info-item {
        background: #ffffff;
        border: 1px solid #e2e8f0;
        border-radius: 8px;
        padding: 16px;
        display: flex;
        align-items: flex-start;
        gap: 12px;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.02);
        transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        position: relative;
        overflow: hidden;

        &::before {
          content: "";
          position: absolute;
          top: 0;
          left: 0;
          width: 100%;
          height: 3px;
          background: linear-gradient(90deg, #4299e1, #3182ce);
          transform: scaleX(0);
          transition: transform 0.3s ease;
        }

        &:hover {
          border-color: #4299e1;
          box-shadow: 0 8px 25px rgba(66, 153, 225, 0.15);
          transform: translateY(-3px);

          &::before {
            transform: scaleX(1);
          }

          .info-icon {
            transform: scale(1.1);
          }
        }

        // 优先级高的项目（事件标题）
        &.priority-high {
          grid-column: 1 / -1;
          background: linear-gradient(135deg, #f7fafc 0%, #edf2f7 100%);
          border-color: #cbd5e0;

          &::before {
            background: linear-gradient(90deg, #667eea, #764ba2);
          }

          .info-value {
            font-size: 16px;
            font-weight: 700;
            color: #2d3748;
          }
        }

        // 伤亡情况特殊样式
        &.casualty-item {
          border-color: #fed7d7;
          background: linear-gradient(135deg, #fffaf0 0%, #fef5e7 100%);

          &::before {
            background: linear-gradient(90deg, #f56565, #e53e3e);
          }

          .casualty-icon {
            color: #e53e3e;
          }
        }

        // 地点项目
        &.location-item {
          .location-icon {
            color: #38a169;
          }
        }

        .info-icon {
          width: 32px;
          height: 32px;
          border-radius: 6px;
          display: flex;
          align-items: center;
          justify-content: center;
          background: linear-gradient(135deg, #e6fffa 0%, #b2f5ea 100%);
          color: #319795;
          font-size: 16px;
          flex-shrink: 0;
          transition: all 0.3s ease;

          &.type-icon {
            background: linear-gradient(135deg, #e6f3ff 0%, #bee3f8 100%);
            color: #3182ce;
          }

          &.casualty-icon {
            background: linear-gradient(135deg, #fed7d7 0%, #feb2b2 100%);
            color: #e53e3e;
          }

          &.time-icon {
            background: linear-gradient(135deg, #faf5ff 0%, #e9d8fd 100%);
            color: #805ad5;
          }

          &.location-icon {
            background: linear-gradient(135deg, #f0fff4 0%, #c6f6d5 100%);
            color: #38a169;
          }
        }

        .info-content {
          flex: 1;
          min-width: 0;

          .info-label {
            font-size: 11px;
            color: #718096;
            margin-bottom: 2px;
            font-weight: 500;
            text-transform: uppercase;
            letter-spacing: 0.3px;
          }

          .info-value {
            font-size: 13px;
            color: #2d3748;
            font-weight: 600;
            line-height: 1.3;
            word-break: break-word;

            &.title-text {
              font-size: 14px;
              font-weight: 700;
              color: #1a202c;
            }

            &.type-text {
              color: #3182ce;
              background: rgba(49, 130, 206, 0.1);
              padding: 2px 8px;
              border-radius: 4px;
              display: inline-block;
            }

            &.casualty-info {
              color: #e53e3e;
              font-weight: 700;
              font-size: 13px;
            }

            &.time-text {
              color: #805ad5;
              font-family: "Monaco", "Menlo", monospace;
            }

            &.location-text {
              color: #38a169;
            }
          }
        }
      }
    }
  }

  .process-section {
    height: 380px;
    background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
    border: 1px solid #e2e8f0;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
    transition: all 0.3s ease;

    &:hover {
      box-shadow: 0 4px 16px rgba(0, 0, 0, 0.06);
      transform: translateY(-1px);
    }
  }
}

// 右侧预留区域
.right-content {
  height: 100%;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  //min-width: 400px; /* 设置最小宽度 */
}

// 过程信息区域样式重写
.process-section {
  background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
  border: 1px solid #e2e8f0;
  border-radius: 8px;
  flex: 1;
  min-height: 380px;
  display: flex;
  flex-direction: column;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
  overflow: hidden;

  .section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px 16px;
    border-bottom: 1px solid #e2e8f0;
    background: linear-gradient(135deg, #f7fafc 0%, #edf2f7 100%);
    position: relative;

    &::after {
      content: "";
      position: absolute;
      bottom: 0;
      left: 0;
      width: 100%;
      height: 2px;
      background: linear-gradient(90deg, #4299e1, #3182ce, #805ad5);
    }

    .section-title-area {
      display: flex;
      flex-direction: column;
      align-items: flex-start;
    }

    .section-title {
      font-size: 16px;
      font-weight: 700;
      color: #1a202c;
      margin: 0;
      display: flex;
      align-items: center;
      cursor: pointer;
      transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
      padding: 6px 8px;
      border-radius: 6px;
      position: relative;

      &.sortable {
        &:hover {
          background: linear-gradient(
            135deg,
            rgba(66, 153, 225, 0.1) 0%,
            rgba(49, 130, 206, 0.1) 100%
          );
          color: #3182ce;
          transform: translateY(-2px);
          box-shadow: 0 4px 12px rgba(66, 153, 225, 0.2);
        }
      }

      i {
        margin-right: 10px;
        color: #4299e1;
        font-size: 20px;
        transition: all 0.3s ease;
      }

      &:hover i {
        color: #3182ce;
        transform: scale(1.1);
      }

      .sort-icon {
        margin-left: 10px;
        margin-right: 0;
        font-size: 16px;
        color: #718096;
        transition: all 0.3s ease;
        background: rgba(113, 128, 150, 0.1);
        padding: 4px;
        border-radius: 4px;
      }

      &:hover .sort-icon {
        color: #3182ce;
        background: rgba(49, 130, 206, 0.1);
        transform: rotate(180deg);
      }
    }

    .sort-indicator {
      font-size: 12px;
      color: #4a5568;
      margin-top: 4px;
      margin-left: 16px;
      background: linear-gradient(
        135deg,
        rgba(66, 153, 225, 0.1) 0%,
        rgba(49, 130, 206, 0.1) 100%
      );
      padding: 4px 10px;
      border-radius: 6px;
      font-weight: 600;
      border: 1px solid rgba(66, 153, 225, 0.2);
      text-transform: uppercase;
      letter-spacing: 0.5px;
    }

    .header-controls {
      display: flex;
      align-items: center;
      gap: 20px;

      .filter-controls {
        display: flex;
        align-items: center;
        gap: 12px;

        .el-select {
          .el-input__inner {
            border-radius: 8px;
            border: 1px solid #e2e8f0;
            transition: all 0.3s ease;

            &:hover {
              border-color: #4299e1;
            }

            &:focus {
              border-color: #3182ce;
              box-shadow: 0 0 0 3px rgba(66, 153, 225, 0.1);
            }
          }
        }

        .el-button {
          border-radius: 8px;
          border: 1px solid #e2e8f0;
          background: #ffffff;
          color: #4a5568;
          transition: all 0.3s ease;

          &:hover {
            border-color: #4299e1;
            color: #3182ce;
            background: rgba(66, 153, 225, 0.05);
            transform: translateY(-1px);
          }
        }
      }

      .control-actions {
        display: flex;
        align-items: center;
        gap: 12px;

        .el-checkbox {
          font-size: 14px;
          font-weight: 500;

          .el-checkbox__label {
            color: #4a5568;
          }

          &:hover .el-checkbox__label {
            color: #3182ce;
          }
        }
      }
    }
  }
}

// 过程信息区域样式
.process-section {
  background: #fff;
  border: 1px solid #e9ecef;
  border-radius: 8px;
  height: 500px;
  display: flex;
  flex-direction: column;

  .process-timeline-container {
    flex: 1;
    overflow-y: auto;
    padding: 16px 20px;
  }
}

// 过程信息时间轴样式
.process-timeline {
  position: relative;

  // 动态主时间轴线 - 根据模块数量动态调整
  &::before {
    content: "";
    position: absolute;
    left: -10px;
    top: 50px;
    width: 2px;
    background: linear-gradient(to bottom, #409eff 0%, #176ec5 100%);
    z-index: 1;
    // 使用CSS变量动态设置高度
    height: var(--timeline-height, 0px);
    transition: height 0.3s ease;
  }

  .process-item {
    display: flex;
    margin-bottom: 16px;
    background: linear-gradient(135deg, #ffffff 0%, #fafbfc 100%);
    border: 1px solid #e2e8f0;
    border-radius: 12px;
    transition: all 0.4s cubic-bezier(0.25, 0.8, 0.25, 1);
    position: relative;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
    padding: 14px;
    align-items: flex-start;
    overflow: hidden;

    &::before {
      content: "";
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background: linear-gradient(
        135deg,
        rgba(66, 153, 225, 0.02) 0%,
        rgba(49, 130, 206, 0.02) 100%
      );
      opacity: 0;
      transition: opacity 0.3s ease;
    }

    &:hover {
      border-color: #4299e1;
      box-shadow: 0 8px 20px rgba(66, 153, 225, 0.15);
      transform: translateY(-2px);

      &::before {
        opacity: 1;
      }

      .process-number-wrapper {
        .process-number {
          transform: scale(1.1);
          box-shadow: 0 6px 16px rgba(66, 153, 225, 0.35);

          &::before {
            opacity: 1;
            transform: scale(1);
          }

          &::after {
            opacity: 1;
            transform: scale(1);
          }
        }

        .process-line {
          opacity: 1;
          background: linear-gradient(
            to bottom,
            #4299e1 0%,
            #3182ce 50%,
            #805ad5 100%
          );
        }
      }
    }

    &:last-child {
      margin-bottom: 0;
    }

    // 序号包装器
    .process-number-wrapper {
      display: flex;
      flex-direction: column;
      align-items: center;
      margin-right: 16px;
      position: relative;
      flex-shrink: 0;
      z-index: 2;

      .process-number {
        width: 36px;
        height: 36px;
        border-radius: 50%;
        background: linear-gradient(135deg, #4299e1 0%, #3182ce 100%);
        color: #ffffff;
        font-size: 16px;
        font-weight: 700;
        display: flex;
        align-items: center;
        justify-content: center;
        position: relative;
        box-shadow: 0 4px 12px rgba(66, 153, 225, 0.3);
        border: 2px solid #ffffff;
        transition: all 0.4s cubic-bezier(0.25, 0.8, 0.25, 1);

        // 数字圆圈的光晕效果
        &::before {
          content: "";
          position: absolute;
          top: -3px;
          left: -3px;
          right: -3px;
          bottom: -3px;
          border-radius: 50%;
          background: linear-gradient(
            135deg,
            rgba(66, 153, 225, 0.2) 0%,
            rgba(49, 130, 206, 0.2) 100%
          );
          z-index: -1;
          opacity: 0;
          transition: all 0.4s ease;
          transform: scale(0.8);
        }

        &::after {
          content: "";
          position: absolute;
          top: -2px;
          left: -2px;
          right: -2px;
          bottom: -2px;
          border-radius: 50%;
          border: 2px solid rgba(66, 153, 225, 0.3);
          opacity: 0;
          transition: all 0.4s ease;
          transform: scale(1.2);
        }
      }

      .process-line {
        width: 2px;
        background: linear-gradient(to bottom, #4299e1 0%, #3182ce 100%);
        margin-top: 6px;
        flex: 1;
        min-height: 40px;
        opacity: 0.6;
        transition: all 0.3s ease;
      }
    }

    // 内容包装器
    .process-content-wrapper {
      display: flex;
      flex: 1;
      align-items: flex-start;
      gap: 12px;
    }

    .process-left {
      display: flex;
      flex-direction: column;
      align-items: center;
      margin-right: 20px;
      position: relative;
      z-index: 3;

      .process-number {
        width: 40px;
        height: 40px;
        border-radius: 50%;
        background: linear-gradient(135deg, #409eff 0%, #1976d2 100%);
        color: #fff;
        font-size: 16px;
        font-weight: 700;
        display: flex;
        align-items: center;
        justify-content: center;
        flex-shrink: 0;
        position: relative;
        box-shadow: 0 4px 12px rgba(64, 158, 255, 0.3);
        border: 3px solid #fff;
        transition: all 0.3s ease;

        // 数字圆圈的光晕效果
        &::before {
          content: "";
          position: absolute;
          top: -3px;
          left: -3px;
          right: -3px;
          bottom: -3px;
          border-radius: 50%;
          background: linear-gradient(
            135deg,
            rgba(64, 158, 255, 0.2) 0%,
            rgba(25, 118, 210, 0.2) 100%
          );
          z-index: -1;
          opacity: 0;
          transition: all 0.3s ease;
        }
      }

      // 移除原来的连接线，因为现在使用主时间轴线
      .process-line {
        display: none;
      }
    }

    // 悬停和激活状态的数字样式
    &:hover .process-left .process-number {
      transform: scale(1.1);
      box-shadow: 0 6px 20px rgba(64, 158, 255, 0.4);

      &::before {
        opacity: 1;
        transform: scale(1.2);
      }
    }

    &.active .process-left .process-number {
      background: linear-gradient(135deg, #ff6b35 0%, #f7931e 100%);
      transform: scale(1.15);
      box-shadow: 0 8px 25px rgba(255, 107, 53, 0.4);

      &::before {
        opacity: 1;
        transform: scale(1.3);
        background: linear-gradient(
          135deg,
          rgba(255, 107, 53, 0.3) 0%,
          rgba(247, 147, 30, 0.3) 100%
        );
      }
    }

    .process-right {
      flex: 1;
      position: relative;
      padding-left: 4px;

      .process-header {
        display: flex;
        justify-content: space-between;
        align-items: flex-start;
        margin-bottom: 12px;
        flex-wrap: wrap;
        gap: 8px;

        .process-time {
          font-size: 15px;
          color: #1a1a1a;
          font-weight: 700;
          background: linear-gradient(135deg, #409eff 0%, #1976d2 100%);
          -webkit-background-clip: text;
          -webkit-text-fill-color: transparent;
          background-clip: text;
          flex-shrink: 0;
        }

        .process-unit {
          font-size: 13px;
          color: #409eff;
          font-weight: 600;
          background: rgba(64, 158, 255, 0.1);
          padding: 4px 10px;
          border-radius: 16px;
          border: 1px solid rgba(64, 158, 255, 0.2);
          flex-shrink: 0;
        }

        .process-source {
          font-size: 11px;
          color: #666;
          background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
          padding: 4px 10px;
          border-radius: 14px;
          border: 1px solid #e9ecef;
          font-weight: 500;
          flex-shrink: 0;
        }
      }

      .process-content {
        .process-description {
          font-size: 14px;
          color: #4a5568;
          line-height: 1.6;
          margin-bottom: 0;
          padding: 8px 0;
          border-left: 3px solid transparent;
          padding-left: 12px;
          transition: all 0.3s ease;
          background: rgba(248, 250, 252, 0.5);
          border-radius: 0 8px 8px 0;
          margin-left: -12px;
          padding-left: 16px;
        }
      }

      .process-expand-icon {
        position: absolute;
        right: 16px;
        top: 75%;
        transform: translateY(-50%);
        color: #cbd5e0;
        font-size: 18px;
        transition: all 0.3s ease;
        cursor: pointer;
        width: 32px;
        height: 32px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        background: rgba(255, 255, 255, 0.8);
        border: 1px solid #e2e8f0;

        i {
          transition: transform 0.3s ease;
        }

        &:hover {
          color: #409eff;
          background: rgba(64, 158, 255, 0.1);
          border-color: #409eff;
          transform: translateY(-50%) scale(1.1);
        }
      }
    }

    // 悬停状态的内容样式
    &:hover .process-right .process-content .process-description {
      border-left-color: #409eff;
      background: rgba(64, 158, 255, 0.05);
    }

    // 激活状态的内容样式
    &.active .process-right .process-content .process-description {
      border-left-color: #ff6b35;
      background: rgba(255, 107, 53, 0.05);
      color: #2d3748;
      font-weight: 500;
    }

    // 新布局样式
    // 左侧信息区：时间和操作人
    .process-left-info {
      display: flex;
      flex-direction: column;
      min-width: 100px;
      flex-shrink: 0;
      padding: 2px 0;

      .process-time {
        font-size: 13px;
        color: #666;
        margin-bottom: 4px;
        font-weight: 500;
      }

      .process-operator {
        font-size: 12px;
        color: #999;
        font-weight: 400;
      }
    }

    // 中间内容区：详细信息
    .process-main-content {
      flex: 1;
      cursor: pointer;
      padding: 8px 12px;
      border-radius: 8px;
      transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
      position: relative;
      background: rgba(248, 250, 252, 0.3);
      border: 1px solid transparent;

      &::before {
        content: "";
        position: absolute;
        left: 0;
        top: 0;
        bottom: 0;
        width: 3px;
        background: linear-gradient(to bottom, #4299e1, #3182ce);
        border-radius: 2px;
        opacity: 0;
        transition: opacity 0.3s ease;
      }

      &:hover {
        background: rgba(66, 153, 225, 0.05);
        border-color: rgba(66, 153, 225, 0.2);
        transform: translateX(3px);

        &::before {
          opacity: 1;
        }
      }

      .process-description {
        font-size: 14px;
        color: #2d3748;
        line-height: 1.5;
        margin: 0;
        font-weight: 500;
        transition: color 0.3s ease;
      }

      &:hover .process-description {
        color: #1a202c;
      }
    }

    // 右侧功能区：操作类型和复选框
    .process-right-actions {
      display: flex;
      flex-direction: column;
      align-items: flex-end;
      gap: 8px;
      min-width: 90px;
      flex-shrink: 0;
      padding: 2px 0;

      .operation-type {
        font-size: 11px;
        color: #ffffff;
        background: linear-gradient(135deg, #4299e1 0%, #3182ce 100%);
        padding: 4px 8px;
        border-radius: 6px;
        font-weight: 600;
        white-space: nowrap;
        box-shadow: 0 2px 6px rgba(66, 153, 225, 0.25);
        transition: all 0.3s ease;
        text-transform: uppercase;
        letter-spacing: 0.3px;

        &:hover {
          background: linear-gradient(135deg, #3182ce 0%, #2c5aa0 100%);
          transform: translateY(-1px);
          box-shadow: 0 3px 8px rgba(66, 153, 225, 0.35);
        }
      }

      .el-checkbox {
        transform: scale(1.1);
        transition: all 0.3s ease;

        &:hover {
          transform: scale(1.2);
        }
      }
    }
  }

  // 步骤轴进入动画
  .process-item {
    animation: slideInFromLeft 0.6s ease-out both;

    &:nth-child(1) {
      animation-delay: 0.1s;
    }
    &:nth-child(2) {
      animation-delay: 0.2s;
    }
    &:nth-child(3) {
      animation-delay: 0.3s;
    }
    &:nth-child(4) {
      animation-delay: 0.4s;
    }
    &:nth-child(5) {
      animation-delay: 0.5s;
    }
    &:nth-child(n + 6) {
      animation-delay: 0.6s;
    }
  }
}

// 步骤轴动画关键帧
@keyframes slideInFromLeft {
  0% {
    opacity: 0;
    transform: translateX(-50px);
  }
  100% {
    opacity: 1;
    transform: translateX(0);
  }
}

// 数字脉动动画
@keyframes pulse {
  0%,
  100% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.05);
  }
}

// 激活状态的数字添加脉动效果
.process-timeline .process-item.active .process-left .process-number {
  animation: pulse 2s infinite;
}

// 过程详情弹框样式
.process-detail-dialog {
  z-index: 3000 !important;

  .el-dialog {
    margin-top: 5vh !important;
  }

  .el-dialog__wrapper {
    z-index: 3000 !important;
  }

  .el-dialog__body {
    padding: 0;
  }
}

.process-detail-content {
  min-height: 400px;
  max-height: 70vh;
  overflow-y: auto;

  .detail-content-area {
    padding: 20px;

    .detail-item {
      margin-bottom: 16px;
      padding: 8px 0;
      border-radius: 4px;
      transition: all 0.3s ease;

      &:hover {
        background: rgba(64, 158, 255, 0.02);
        padding-left: 8px;
        margin-left: -8px;
        margin-right: -8px;
      }

      &.full-content {
        margin-bottom: 20px;
      }

      .detail-label {
        font-size: 14px;
        color: #666;
        margin-bottom: 6px;
        font-weight: 500;
        transition: color 0.3s ease;
      }

      .detail-value {
        font-size: 14px;
        color: #333;
        line-height: 1.5;
        transition: all 0.3s ease;

        &.detail-textarea {
          background: #f8f9fa;
          border: 1px solid #e9ecef;
          border-radius: 4px;
          padding: 12px;
          min-height: 80px;
          line-height: 1.6;
          transition: all 0.3s ease;

          &:hover {
            background: #f0f7ff;
            border-color: #409eff;
          }
        }
      }
    }

    .casualty-summary {
      display: flex;
      flex-wrap: wrap;
      gap: 12px;
      margin-top: 16px;
      padding: 12px;
      background: linear-gradient(135deg, #f8f9fa 0%, #f0f7ff 100%);
      border-radius: 6px;
      border: 1px solid #e9ecef;
      transition: all 0.3s ease;

      &:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 12px rgba(64, 158, 255, 0.1);
      }

      .casualty-item {
        display: flex;
        align-items: center;
        transition: transform 0.3s ease;

        &:hover {
          transform: scale(1.05);
        }

        .casualty-label {
          font-size: 13px;
          color: #666;
          margin-right: 4px;
        }

        .casualty-number {
          font-size: 16px;
          font-weight: 600;
          padding: 4px 10px;
          border-radius: 6px;
          transition: all 0.3s ease;
          cursor: default;

          &:hover {
            transform: scale(1.1);
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
          }

          &.death {
            color: #f56c6c;
            background: linear-gradient(135deg, #fef0f0 0%, #fde2e2 100%);
            border: 1px solid #fecaca;
          }

          &.missing {
            color: #e6a23c;
            background: linear-gradient(135deg, #fdf6ec 0%, #fed7aa 100%);
            border: 1px solid #fde68a;
          }

          &.severe {
            color: #f56c6c;
            background: linear-gradient(135deg, #fef0f0 0%, #fde2e2 100%);
            border: 1px solid #fecaca;
          }

          &.light {
            color: #67c23a;
            background: linear-gradient(135deg, #f0f9eb 0%, #dcfce7 100%);
            border: 1px solid #bbf7d0;
          }
        }
      }
    }
  }

  .no-selection {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 40px 20px;
    color: #999;
    transition: all 0.3s ease;

    i {
      font-size: 48px;
      margin-bottom: 12px;
      color: #ddd;
      transition: all 0.3s ease;
      animation: pulse 2s infinite;
    }

    p {
      font-size: 14px;
      margin: 0;
      transition: color 0.3s ease;
    }

    &:hover {
      color: #666;

      i {
        color: #409eff;
        transform: scale(1.1);
      }
    }
  }
}

@keyframes pulse {
  0%,
  100% {
    opacity: 0.6;
    transform: scale(1);
  }
  50% {
    opacity: 1;
    transform: scale(1.05);
  }
}

@keyframes rotate {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

.no-data {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 300px;
  color: #909399;

  i {
    font-size: 48px;
    margin-bottom: 15px;
  }

  p {
    font-size: 14px;
  }
}

// 详情项动画
.detail-content-area .detail-item,
.detail-content-area .casualty-summary {
  animation: fadeInUp 0.3s ease-out both;
}

@keyframes fadeInUp {
  0% {
    opacity: 0;
    transform: translateY(20px);
  }
  100% {
    opacity: 1;
    transform: translateY(0);
  }
}

// 响应式处理
@media (max-width: 1400px) {
  .header-info .info-items-container {
    gap: 8px;

    .info-item {
      min-width: 60px;
      padding: 6px 10px;

      .info-label {
        font-size: 11px;
        padding: 1px 4px;
        margin-right: 6px;
      }

      .info-value {
        font-size: 12px;
      }

      &.full-width .info-value {
        max-width: calc(100% - 70px);
      }
    }
  }
}

@media (max-width: 1200px) {
  .header-info .info-items-container {
    flex-wrap: wrap;

    .info-item.full-width {
      flex: 1 1 100%;
      margin-top: 6px;
      white-space: normal;

      .info-value {
        white-space: normal;
        overflow: visible;
        text-overflow: unset;
        max-width: none;
      }
    }
  }
}

// 新的时间轴样式
.process-timeline-item {
  display: flex;
  margin-bottom: 20px;
  position: relative;

  &:last-child {
    margin-bottom: 0;
  }

  // 时间轴序号和连接线
  .timeline-number-wrapper {
    display: flex;
    flex-direction: column;
    align-items: center;
    margin-right: 20px;
    position: relative;
    flex-shrink: 0;

    .timeline-number {
      width: 36px;
      height: 36px;
      border-radius: 50%;
      background: linear-gradient(135deg, #409eff 0%, #1976d2 100%);
      color: #fff;
      font-size: 14px;
      font-weight: 600;
      display: flex;
      align-items: center;
      justify-content: center;
      box-shadow: 0 2px 8px rgba(64, 158, 255, 0.3);
      border: 2px solid #fff;
      z-index: 2;
      position: relative;
    }

    .timeline-line {
      position: absolute;
      top: 36px;
      left: 50%;
      transform: translateX(-50%);
      width: 2px;
      height: 60px;
      background: linear-gradient(to bottom, #409eff 0%, #e9ecef 100%);
      z-index: 1;
    }
  }

  // 内容区域
  .timeline-content {
    flex: 1;
    background: #ffffff;
    border: 1px solid #e9ecef;
    border-radius: 8px;
    padding: 16px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
    transition: all 0.3s ease;
    display: flex;
    gap: 16px;

    &:hover {
      border-color: #409eff;
      box-shadow: 0 4px 12px rgba(64, 158, 255, 0.15);
      transform: translateY(-1px);
    }

    // 左侧区域
    .timeline-left {
      flex: 1;

      .timeline-header {
        display: flex;
        align-items: center;
        gap: 12px;
        margin-bottom: 8px;

        .timeline-time {
          font-size: 13px;
          color: #409eff;
          font-weight: 600;
          font-family: "Monaco", "Menlo", monospace;
        }

        .timeline-unit {
          font-size: 12px;
          color: #666;
          background: #f5f7fa;
          padding: 2px 8px;
          border-radius: 4px;
          border: 1px solid #e4e7ed;
        }
      }

      .timeline-description {
        font-size: 14px;
        color: #333;
        line-height: 1.5;
        cursor: pointer;
        transition: color 0.3s ease;

        &:hover {
          color: #409eff;
        }
      }
    }

    // 右侧区域
    .timeline-right {
      flex-shrink: 0;
      display: flex;
      flex-direction: column;
      justify-content: flex-start;

      .timeline-actions {
        display: flex;
        align-items: center;
        gap: 12px;

        .timeline-type {
          font-size: 12px;
          color: #409eff;
          background: rgba(64, 158, 255, 0.1);
          padding: 4px 8px;
          border-radius: 4px;
          border: 1px solid rgba(64, 158, 255, 0.2);
          white-space: nowrap;
        }

        .timeline-checkbox {
          flex-shrink: 0;
        }
      }
    }
  }
}
</style>
